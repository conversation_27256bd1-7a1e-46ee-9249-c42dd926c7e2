<template>
  <div class="tnm-staging-record-container">
    <!-- 左侧患者信息详情面板 -->
    <PatientInfoPanel :patient="patient" />

    <!-- 右侧TNM分期记录内容区域 -->
    <div class="record-content-area">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container" v-loading="loading" element-loading-text="正在加载TNM分期记录...">
        <div style="height: 200px;"></div>
      </div>

      <!-- 内容区域 -->
      <div v-else>
        <!-- 页面标题、时间信息和按钮行 -->
        <div class="page-header">
          <h2 class="title-text">TNM分期记录</h2>
          <div class="header-actions">

            <div class="form-actions">
              <el-button
                class="delete-btn"
                size="large"
                :loading="deleteLoading"
                @click="handleDelete"
                :disabled="!recordData.recordSn"
              >
                删除
              </el-button>
              <el-button
                type="primary"
                size="large"
                :loading="saveLoading"
                @click="handleSave"
                class="save-btn"
              >
                保存
              </el-button>
            </div>
          </div>
        </div>


        <!-- TNM分期记录表单 -->
        <div class="record-form">
          <!-- 临床诊断 -->
          <div class="form-group">
            <label class="form-label">临床诊断：</label>
            <el-select
              v-model="formData.diagnosisItemCode"
              placeholder="请选择"
              size="large"
              class="form-select"
              :loading="diagnosisLoading"
              @change="handleDiagnosisChange"
            >
              <el-option
                v-for="item in diagnosisOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>

          <!-- 肿瘤TNM分期 -->
          <div class="form-group">
            <label class="form-label">肿瘤TNM：</label>
            <div class="tnm-inputs">
              <el-select
                v-model="formData.tnmTStaging"
                placeholder="请选择T分期"
                size="large"
                class="form-select-tnm"
                :class="{ 'custom-disabled': !isTnmEditable }"
                :disabled="!isTnmEditable"
                :loading="tnmTLoading"
                clearable
              >
                <el-option
                  v-for="item in tnmTOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-select
                v-model="formData.tnmNStaging"
                placeholder="请选择N分期"
                size="large"
                class="form-select-tnm"
                :class="{ 'custom-disabled': !isTnmEditable }"
                :disabled="!isTnmEditable"
                :loading="tnmNLoading"
                clearable
              >
                <el-option
                  v-for="item in tnmNOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-select
                v-model="formData.tnmMStaging"
                placeholder="请选择M分期"
                size="large"
                class="form-select-tnm"
                :class="{ 'custom-disabled': !isTnmEditable }"
                :disabled="!isTnmEditable"
                :loading="tnmMLoading"
                clearable
              >
                <el-option
                  v-for="item in tnmMOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>

          <!-- FIGO分期-宫颈癌 -->
          <div class="form-group">
            <label class="form-label">FIGO分期-宫颈癌：</label>
            <el-select
              v-model="formData.figoStagingCervicalCancer"
              placeholder="请选择FIGO分期"
              size="large"
              class="form-select"
              :class="{ 'custom-disabled': !isFigoEditable }"
              :disabled="!isFigoEditable"
              :loading="figoLoading"
              clearable
            >
              <el-option
                v-for="item in figoOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>

          <!-- CNLC分期-肝细胞癌 -->
          <div class="form-group">
            <label class="form-label">CNLC分期-肝细胞癌：</label>
            <el-select
              v-model="formData.cnlcStagingHepatocellular"
              placeholder="请选择CNLC分期"
              size="large"
              class="form-select"
              :class="{ 'custom-disabled': !isCnlcEditable }"
              :disabled="!isCnlcEditable"
              :loading="cnlcLoading"
              clearable
            >
              <el-option
                v-for="item in cnlcOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>

          <!-- Ann Arbor分期-淋巴瘤 -->
          <div class="form-group">
            <label class="form-label">Ann Arbor分期-淋巴瘤：</label>
            <el-select
              v-model="formData.annArborStagingLymphoma"
              placeholder="请选择Ann Arbor分期"
              size="large"
              class="form-select"
              :class="{ 'custom-disabled': !isAnnArborEditable }"
              :disabled="!isAnnArborEditable"
              :loading="annArborLoading"
              clearable
            >
              <el-option
                v-for="item in annArborOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>

          <!-- 分期时间 -->
          <div class="form-group">
            <label class="form-label">分期时间：</label>
            <el-date-picker
              v-model="formData.stagingDatetime"
              type="datetime"
              placeholder="请输入"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              size="large"
              class="form-date-picker"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter, useRoute } from 'vue-router'
import { apiServices } from '@/api'
import { useDictionaryStore } from '@/stores'
import { getAllUrlParams } from '@/utils/urlParams'
import PatientInfoPanel from './PatientInfoPanel.vue'

// 定义组件props
const props = defineProps({
  patient: {
    type: Object,
    required: true,
    default: () => ({})
  }
})

// 使用路由
const router = useRouter()
const route = useRoute()

// 使用字典Store
const dictionaryStore = useDictionaryStore()

// 检查是否来自医嘱页面
const isFromMedicalOrder = computed(() => {
  return route.query.fromMedicalOrder === 'true'
})

// 响应式数据
const loading = ref(false)
const saveLoading = ref(false)
const deleteLoading = ref(false)

// 字典数据相关状态
const diagnosisOptions = ref([])
const diagnosisLoading = ref(false)

// TNM分期字典数据
const tnmTOptions = ref([])
const tnmNOptions = ref([])
const tnmMOptions = ref([])
const tnmTLoading = ref(false)
const tnmNLoading = ref(false)
const tnmMLoading = ref(false)

// FIGO分期字典数据
const figoOptions = ref([])
const figoLoading = ref(false)

// CNLC分期字典数据
const cnlcOptions = ref([])
const cnlcLoading = ref(false)

// Ann Arbor分期字典数据
const annArborOptions = ref([])
const annArborLoading = ref(false)

// 跟踪之前选择的诊断类型
const previousDiagnosis = ref('')

// TNM分期记录数据
const recordData = reactive({
  recordDatetime: '',
  recordUpdateDatetime: '',
  recordSn: '',
  visitSn: ''
})

// 表单数据
const formData = reactive({
  diagnosisItemCode: '',
  tnmTStaging: '',
  tnmNStaging: '',
  tnmMStaging: '',
  figoStagingCervicalCancer: '',
  cnlcStagingHepatocellular: '',
  annArborStagingLymphoma: '',
  stagingDatetime: null
})

// 临时存储用户输入的数据（按具体诊断名称分组）
const tempFormData = reactive({
  // 存储格式：{ '诊断名称': { 分期字段: 值 } }
  // 例如：{ '宫颈癌': { figoStagingCervicalCancer: 'FIGO IA' } }
})

// ========== 计算属性：控制输入框可编辑状态 ==========

// 获取当前选择的诊断名称
const currentDiagnosisName = computed(() => {
  if (!formData.diagnosisItemCode) return ''
  const diagnosis = diagnosisOptions.value.find(item => item.value === formData.diagnosisItemCode)
  return diagnosis ? diagnosis.label : ''
})

// TNM分期输入框是否可编辑（只有非特殊癌症类型才可编辑TNM）
const isTnmEditable = computed(() => {
  const diagnosisName = currentDiagnosisName.value
  return !!formData.diagnosisItemCode &&
         diagnosisName !== '宫颈癌' &&
         diagnosisName !== '卵巢癌' &&
         diagnosisName !== '肝细胞癌' &&
         diagnosisName !== '淋巴瘤'
})

// FIGO分期输入框是否可编辑（只有宫颈癌、卵巢癌可编辑）
const isFigoEditable = computed(() => {
  const diagnosisName = currentDiagnosisName.value
  return diagnosisName === '宫颈癌' || diagnosisName === '卵巢癌'
})

// CNLC分期输入框是否可编辑（只有肝细胞癌可编辑）
const isCnlcEditable = computed(() => {
  const diagnosisName = currentDiagnosisName.value
  return diagnosisName === '肝细胞癌'
})

// Ann Arbor分期输入框是否可编辑（只有淋巴瘤可编辑）
const isAnnArborEditable = computed(() => {
  const diagnosisName = currentDiagnosisName.value
  return diagnosisName === '淋巴瘤'
})

// ========== 字典数据加载方法 ==========

/**
 * 加载诊断名称字典
 */
const loadDiagnosisOptions = async () => {
  try {
    diagnosisLoading.value = true
    console.log('正在加载临床诊断字典...')

    // 调用字典服务获取临床诊断数据
    const data = await apiServices.dictionary.getByType('clinical_diagnosis')

    // 转换数据格式为Element Plus Select组件需要的格式
    diagnosisOptions.value = data.map(item => ({
      label: item.label,
      value: item.value
    }))

    console.log('临床诊断字典加载成功:', diagnosisOptions.value)
  } catch (error) {
    console.error('加载临床诊断字典失败:', error)
    diagnosisOptions.value = []
    ElMessage.error('加载诊断选项失败: ' + error.message)
  } finally {
    diagnosisLoading.value = false
  }
}

/**
 * 加载TNM分期字典数据
 */
const loadTnmStagingOptions = async (diagnosisName) => {
  if (!diagnosisName) return

  try {
    // 并行加载T、N、M分期数据
    const [tPromise, nPromise, mPromise] = [
      loadTnmTOptions(diagnosisName),
      loadTnmNOptions(diagnosisName),
      loadTnmMOptions(diagnosisName)
    ]

    await Promise.all([tPromise, nPromise, mPromise])
  } catch (error) {
    console.error('加载TNM分期字典失败:', error)
    ElMessage.error('加载TNM分期选项失败: ' + error.message)
  }
}

/**
 * 加载T分期字典
 */
const loadTnmTOptions = async (diagnosisName) => {
  try {
    tnmTLoading.value = true
    const dictType = `${diagnosisName}-T`
    const data = await apiServices.dictionary.getByType(dictType)
    tnmTOptions.value = data.map(item => ({
      label: item.label,
      value: item.value
    }))
    console.log(`T分期字典 ${dictType} 加载成功:`, tnmTOptions.value)
  } catch (error) {
    console.error(`加载T分期字典失败:`, error)
    tnmTOptions.value = []
  } finally {
    tnmTLoading.value = false
  }
}

/**
 * 加载N分期字典
 */
const loadTnmNOptions = async (diagnosisName) => {
  try {
    tnmNLoading.value = true
    const dictType = `${diagnosisName}-N`
    const data = await apiServices.dictionary.getByType(dictType)
    tnmNOptions.value = data.map(item => ({
      label: item.label,
      value: item.value
    }))
    console.log(`N分期字典 ${dictType} 加载成功:`, tnmNOptions.value)
  } catch (error) {
    console.error(`加载N分期字典失败:`, error)
    tnmNOptions.value = []
  } finally {
    tnmNLoading.value = false
  }
}

/**
 * 加载M分期字典
 */
const loadTnmMOptions = async (diagnosisName) => {
  try {
    tnmMLoading.value = true
    const dictType = `${diagnosisName}-M`
    const data = await apiServices.dictionary.getByType(dictType)
    tnmMOptions.value = data.map(item => ({
      label: item.label,
      value: item.value
    }))
    console.log(`M分期字典 ${dictType} 加载成功:`, tnmMOptions.value)
  } catch (error) {
    console.error(`加载M分期字典失败:`, error)
    tnmMOptions.value = []
  } finally {
    tnmMLoading.value = false
  }
}

/**
 * 加载FIGO分期字典
 */
const loadFigoOptions = async (diagnosisName) => {
  if (!diagnosisName) return

  try {
    figoLoading.value = true
    const dictType = `${diagnosisName}-FC`
    const data = await apiServices.dictionary.getByType(dictType)
    figoOptions.value = data.map(item => ({
      label: item.label,
      value: item.value
    }))
    console.log(`FIGO分期字典 ${dictType} 加载成功:`, figoOptions.value)
  } catch (error) {
    console.error(`加载FIGO分期字典 ${dictType} 失败:`, error)
    figoOptions.value = []
  } finally {
    figoLoading.value = false
  }
}

/**
 * 加载CNLC分期字典
 */
const loadCnlcOptions = async () => {
  try {
    cnlcLoading.value = true
    const dictType = '肝细胞癌-FC'
    const data = await apiServices.dictionary.getByType(dictType)
    cnlcOptions.value = data.map(item => ({
      label: item.label,
      value: item.value
    }))
    console.log(`CNLC分期字典加载成功:`, cnlcOptions.value)
  } catch (error) {
    console.error('加载CNLC分期字典失败:', error)
    cnlcOptions.value = []
  } finally {
    cnlcLoading.value = false
  }
}

/**
 * 加载Ann Arbor分期字典
 */
const loadAnnArborOptions = async () => {
  try {
    annArborLoading.value = true
    const dictType = '淋巴瘤-FC'
    const data = await apiServices.dictionary.getByType(dictType)
    annArborOptions.value = data.map(item => ({
      label: item.label,
      value: item.value
    }))
    console.log(`Ann Arbor分期字典加载成功:`, annArborOptions.value)
  } catch (error) {
    console.error('加载Ann Arbor分期字典失败:', error)
    annArborOptions.value = []
  } finally {
    annArborLoading.value = false
  }
}

/**
 * 保存当前表单数据到临时存储
 */
const saveCurrentFormData = (previousDiagnosisCode) => {
  if (!previousDiagnosisCode) return

  // 获取之前诊断的名称
  const previousDiagnosis = diagnosisOptions.value.find(item => item.value === previousDiagnosisCode)
  const previousDiagnosisName = previousDiagnosis ? previousDiagnosis.label : ''

  if (!previousDiagnosisName) return

  // 初始化该诊断的存储对象
  if (!tempFormData[previousDiagnosisName]) {
    tempFormData[previousDiagnosisName] = {}
  }

  // 根据诊断类型保存相应的分期数据
  if (previousDiagnosisName === '宫颈癌' || previousDiagnosisName === '卵巢癌') {
    // 保存FIGO分期数据
    tempFormData[previousDiagnosisName].figoStagingCervicalCancer = formData.figoStagingCervicalCancer
  } else if (previousDiagnosisName === '肝细胞癌') {
    // 保存CNLC分期数据
    tempFormData[previousDiagnosisName].cnlcStagingHepatocellular = formData.cnlcStagingHepatocellular
  } else if (previousDiagnosisName === '淋巴瘤') {
    // 保存Ann Arbor分期数据
    tempFormData[previousDiagnosisName].annArborStagingLymphoma = formData.annArborStagingLymphoma
  } else {
    // 保存TNM分期数据（其他癌种）
    tempFormData[previousDiagnosisName].tnmTStaging = formData.tnmTStaging
    tempFormData[previousDiagnosisName].tnmNStaging = formData.tnmNStaging
    tempFormData[previousDiagnosisName].tnmMStaging = formData.tnmMStaging
  }

  console.log(`保存 ${previousDiagnosisName} 的分期数据:`, tempFormData[previousDiagnosisName])
}

/**
 * 从临时存储恢复表单数据
 */
const restoreFormData = (currentDiagnosisCode) => {
  if (!currentDiagnosisCode) {
    // 没有选择诊断，清空所有分期字段
    clearAllStagingFields()
    return
  }

  // 获取当前诊断的名称
  const currentDiagnosis = diagnosisOptions.value.find(item => item.value === currentDiagnosisCode)
  const currentDiagnosisName = currentDiagnosis ? currentDiagnosis.label : ''

  if (!currentDiagnosisName) {
    clearAllStagingFields()
    return
  }

  // 先清空所有分期字段
  clearAllStagingFields()

  // 检查是否有该诊断的临时数据
  const savedData = tempFormData[currentDiagnosisName]

  if (savedData) {
    // 恢复该诊断的数据
    if (currentDiagnosisName === '宫颈癌' || currentDiagnosisName === '卵巢癌') {
      formData.figoStagingCervicalCancer = savedData.figoStagingCervicalCancer || ''
    } else if (currentDiagnosisName === '肝细胞癌') {
      formData.cnlcStagingHepatocellular = savedData.cnlcStagingHepatocellular || ''
    } else if (currentDiagnosisName === '淋巴瘤') {
      formData.annArborStagingLymphoma = savedData.annArborStagingLymphoma || ''
    } else {
      // 其他癌种，恢复TNM数据
      formData.tnmTStaging = savedData.tnmTStaging || ''
      formData.tnmNStaging = savedData.tnmNStaging || ''
      formData.tnmMStaging = savedData.tnmMStaging || ''
    }
    console.log(`恢复 ${currentDiagnosisName} 的分期数据:`, savedData)
  } else {
    console.log(`${currentDiagnosisName} 没有保存的分期数据，保持清空状态`)
  }
}

/**
 * 清空所有分期字段
 */
const clearAllStagingFields = () => {
  formData.tnmTStaging = ''
  formData.tnmNStaging = ''
  formData.tnmMStaging = ''
  formData.figoStagingCervicalCancer = ''
  formData.cnlcStagingHepatocellular = ''
  formData.annArborStagingLymphoma = ''
}

/**
 * 清空临时存储数据
 */
const clearTempFormData = () => {
  // 清空所有临时存储的数据
  Object.keys(tempFormData).forEach(key => {
    delete tempFormData[key]
  })
  console.log('已清空所有临时存储数据')
}

/**
 * 检查是否有临时数据
 */
const hasTempData = (diagnosisCode) => {
  if (!diagnosisCode) return false

  // 获取诊断名称
  const diagnosis = diagnosisOptions.value.find(item => item.value === diagnosisCode)
  const diagnosisName = diagnosis ? diagnosis.label : ''

  if (!diagnosisName) return false

  // 检查该诊断是否有保存的数据
  const savedData = tempFormData[diagnosisName]
  if (!savedData) return false

  // 检查是否有非空的分期数据
  return Object.values(savedData).some(value => value && value.trim() !== '')
}

/**
 * 处理诊断选择变化
 */
const handleDiagnosisChange = async (value) => {
  console.log('诊断选择变化:', { from: previousDiagnosis.value, to: value })

  // 保存当前表单数据到临时存储
  saveCurrentFormData(previousDiagnosis.value)

  // 更新诊断代码
  formData.diagnosisItemCode = value

  // 获取当前诊断名称
  const currentDiagnosis = diagnosisOptions.value.find(item => item.value === value)
  const currentDiagnosisName = currentDiagnosis ? currentDiagnosis.label : ''

  // 检查新选择的诊断是否有临时数据
  if (hasTempData(value)) {
    // 有临时数据，从临时存储恢复
    restoreFormData(value)
  } else {
    // 没有临时数据，清空所有分期字段（保留诊断和时间）
    formData.tnmTStaging = ''
    formData.tnmNStaging = ''
    formData.tnmMStaging = ''
    formData.figoStagingCervicalCancer = ''
    formData.cnlcStagingHepatocellular = ''
    formData.annArborStagingLymphoma = ''
    // 保留分期时间和诊断选择
  }

  // 根据诊断类型加载相应的字典数据
  if (currentDiagnosisName === '宫颈癌' || currentDiagnosisName === '卵巢癌') {
    // 加载FIGO分期字典，传递具体的诊断名称
    await loadFigoOptions(currentDiagnosisName)
  } else if (currentDiagnosisName === '肝细胞癌') {
    // 加载CNLC分期字典
    await loadCnlcOptions()
  } else if (currentDiagnosisName === '淋巴瘤') {
    // 加载Ann Arbor分期字典
    await loadAnnArborOptions()
  } else if (currentDiagnosisName) {
    // 加载TNM分期字典
    await loadTnmStagingOptions(currentDiagnosisName)
  }

  // 更新之前的诊断类型
  previousDiagnosis.value = value
}

// ========== TNM分期记录数据操作方法 ==========

/**
 * 校验TNM分期完整性
 * 如果选择了需要TNM分期的诊断，则T、N、M三个分期都必须选择
 */
const validateTnmStaging = () => {
  // 如果没有选择诊断，不需要校验
  if (!formData.diagnosisItemCode) {
    return { isValid: true }
  }

  // 获取当前诊断名称
  const diagnosisName = currentDiagnosisName.value

  // 检查是否是需要TNM分期的诊断（非特殊癌症类型）
  const needsTnmStaging = diagnosisName &&
                         diagnosisName !== '宫颈癌' &&
                         diagnosisName !== '卵巢癌' &&
                         diagnosisName !== '肝细胞癌' &&
                         diagnosisName !== '淋巴瘤'

  if (needsTnmStaging) {
    // 检查是否有任何一个TNM分期被选择
    const hasTStaging = formData.tnmTStaging && formData.tnmTStaging.trim() !== ''
    const hasNStaging = formData.tnmNStaging && formData.tnmNStaging.trim() !== ''
    const hasMStaging = formData.tnmMStaging && formData.tnmMStaging.trim() !== ''

    // 如果有任何一个TNM分期被选择，则三个都必须选择
    if (hasTStaging || hasNStaging || hasMStaging) {
      if (!hasTStaging) {
        return {
          isValid: false,
          message: '请选择T分期，TNM分期必须完整填写（T、N、M都必须选择）'
        }
      }
      if (!hasNStaging) {
        return {
          isValid: false,
          message: '请选择N分期，TNM分期必须完整填写（T、N、M都必须选择）'
        }
      }
      if (!hasMStaging) {
        return {
          isValid: false,
          message: '请选择M分期，TNM分期必须完整填写（T、N、M都必须选择）'
        }
      }
    }
  }

  return { isValid: true }
}

// 加载TNM分期记录数据
const loadTnmStagingRecord = async () => {
  if (!props.patient?.visitSn) {
    console.warn('缺少visitSn，无法加载TNM分期记录')
    return
  }

  try {
    loading.value = true
    console.log('正在加载TNM分期记录，visitSn:', props.patient.visitSn)

    const data = await apiServices.tnmStagingRecord.getDetail(props.patient.visitSn)

    // 更新记录数据
    Object.assign(recordData, {
      recordDatetime: data.recordDatetime || '',
      recordUpdateDatetime: data.recordUpdateDatetime || '',
      recordSn: data.recordSn || '',
      visitSn: data.visitSn || props.patient.visitSn
    })

    // 更新表单数据
    Object.assign(formData, {
      diagnosisItemCode: data.diagnosisItemCode || '',
      tnmTStaging: data.tnmTStaging || '',
      tnmNStaging: data.tnmNStaging || '',
      tnmMStaging: data.tnmMStaging || '',
      figoStagingCervicalCancer: data.figoStagingCervicalCancer || '',
      cnlcStagingHepatocellular: data.cnlcStagingHepatocellular || '',
      annArborStagingLymphoma: data.annArborStagingLymphoma || '',
      stagingDatetime: data.stagingDatetime || null
    })

    // 更新之前的诊断类型
    previousDiagnosis.value = data.diagnosisItemCode || ''

    // 将加载的数据同步到临时存储中
    if (data.diagnosisItemCode) {
      // 获取诊断名称
      const diagnosis = diagnosisOptions.value.find(item => item.value === data.diagnosisItemCode)
      const diagnosisName = diagnosis ? diagnosis.label : ''

      if (diagnosisName) {
        // 初始化该诊断的存储对象
        if (!tempFormData[diagnosisName]) {
          tempFormData[diagnosisName] = {}
        }

        // 根据诊断类型保存相应的分期数据
        if (diagnosisName === '宫颈癌' || diagnosisName === '卵巢癌') {
          tempFormData[diagnosisName].figoStagingCervicalCancer = data.figoStagingCervicalCancer || ''
        } else if (diagnosisName === '肝细胞癌') {
          tempFormData[diagnosisName].cnlcStagingHepatocellular = data.cnlcStagingHepatocellular || ''
        } else if (diagnosisName === '淋巴瘤') {
          tempFormData[diagnosisName].annArborStagingLymphoma = data.annArborStagingLymphoma || ''
        } else {
          // 其他癌种，保存TNM数据
          tempFormData[diagnosisName].tnmTStaging = data.tnmTStaging || ''
          tempFormData[diagnosisName].tnmNStaging = data.tnmNStaging || ''
          tempFormData[diagnosisName].tnmMStaging = data.tnmMStaging || ''
        }

        console.log(`同步 ${diagnosisName} 的分期数据到临时存储:`, tempFormData[diagnosisName])
      }

      // 根据诊断类型加载相应的字典数据
      if (diagnosisName === '宫颈癌' || diagnosisName === '卵巢癌') {
        await loadFigoOptions(diagnosisName)
      } else if (diagnosisName === '肝细胞癌') {
        await loadCnlcOptions()
      } else if (diagnosisName === '淋巴瘤') {
        await loadAnnArborOptions()
      } else if (diagnosisName) {
        await loadTnmStagingOptions(diagnosisName)
      }
    }

    console.log('TNM分期记录加载成功:', data)
  } catch (error) {
    console.error('加载TNM分期记录失败:', error)
    ElMessage.error('加载TNM分期记录失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 保存TNM分期记录
const handleSave = async () => {
  if (!props.patient?.visitSn) {
    ElMessage.error('缺少患者信息，无法保存')
    return
  }

  // 校验TNM分期完整性
  const validationResult = validateTnmStaging()
  if (!validationResult.isValid) {
    ElMessage.error(validationResult.message)
    return
  }

  try {
    saveLoading.value = true

    // 构建保存数据
    const saveData = {
      visitSn: props.patient.visitSn,
      recordSn: recordData.recordSn,
      ...formData
    }

    console.log('正在保存TNM分期记录:', saveData)

    const result = await apiServices.tnmStagingRecord.save(saveData)

    // 更新记录信息
    if (result.recordSn) {
      recordData.recordSn = result.recordSn
    }
    if (result.recordDatetime) {
      recordData.recordDatetime = result.recordDatetime
    }
    if (result.recordUpdateDatetime) {
      recordData.recordUpdateDatetime = result.recordUpdateDatetime
    }

    // 保存成功后清空临时存储数据
    clearTempFormData()

    ElMessage.success('保存成功')
    console.log('TNM分期记录保存成功:', result)

    // 如果是从医嘱页面跳转过来的，保存成功后延迟返回医嘱页面
    if (isFromMedicalOrder.value) {
      console.log('从医嘱页面跳转过来，保存成功后将返回医嘱页面')

      // 延迟1.5秒后返回医嘱页面，让用户看到保存成功的消息
      setTimeout(() => {
        console.log('开始返回医嘱页面')

        // 获取当前URL参数，但移除fromMedicalOrder标记
        const currentParams = getAllUrlParams()
        delete currentParams.fromMedicalOrder

        // 返回医嘱页面
        router.push({
          name: 'PatientDetail',
          params: {
            visitSn: props.patient.visitSn
          },
          query: {
            ...currentParams,
            selectedMenuItem: 'medical-orders'
          }
        }).then(() => {
          console.log('成功返回医嘱页面')
        }).catch((error) => {
          console.error('返回医嘱页面失败:', error)
        })
      }, 1500) // 延迟1.5秒
    }
  } catch (error) {
    console.error('保存TNM分期记录失败:', error)
    ElMessage.error('保存失败: ' + error.message)
  } finally {
    saveLoading.value = false
  }
}

// 删除TNM分期记录
const handleDelete = async () => {
  if (!props.patient?.visitSn) {
    ElMessage.error('缺少患者信息，无法删除')
    return
  }

  try {
    await ElMessageBox.confirm(
      '确定要删除这条TNM分期记录吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    deleteLoading.value = true

    console.log('正在删除TNM分期记录，visitSn:', props.patient.visitSn)

    await apiServices.tnmStagingRecord.delete(props.patient.visitSn)

    // 清空表单数据
    Object.assign(formData, {
      diagnosisItemCode: '',
      tnmTStaging: '',
      tnmNStaging: '',
      tnmMStaging: '',
      figoStagingCervicalCancer: '',
      cnlcStagingHepatocellular: '',
      annArborStagingLymphoma: '',
      stagingDatetime: null
    })

    // 清空记录数据
    Object.assign(recordData, {
      recordDatetime: '',
      recordUpdateDatetime: '',
      recordSn: '',
      visitSn: props.patient.visitSn
    })

    ElMessage.success('删除成功')
    console.log('TNM分期记录删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除TNM分期记录失败:', error)
      ElMessage.error('删除失败: ' + error.message)
    }
  } finally {
    deleteLoading.value = false
  }
}

// 监听患者变化，重新加载数据
watch(() => props.patient?.visitSn, async (newVisitSn) => {
  if (newVisitSn) {
    await loadTnmStagingRecord()
  }
}, { immediate: false })

/**
 * 强制设置禁用样式
 */
const forceDisabledStyles = () => {
  // 等待DOM更新后执行
  setTimeout(() => {
    const disabledSelects = document.querySelectorAll('.el-select.is-disabled .el-select__wrapper')
    disabledSelects.forEach(wrapper => {
      wrapper.style.backgroundColor = '#F5F5F5'
      wrapper.style.borderColor = '#E4E7ED'
      wrapper.style.cursor = 'default' // 使用默认光标，不显示禁止图标
      wrapper.style.pointerEvents = 'none' // 禁用所有鼠标事件
    })

    // 为父容器设置默认光标
    const disabledSelectContainers = document.querySelectorAll('.el-select.is-disabled')
    disabledSelectContainers.forEach(container => {
      container.style.cursor = 'default'
      container.style.pointerEvents = 'none'
    })

    const disabledPlaceholders = document.querySelectorAll('.el-select.is-disabled .el-select__placeholder span')
    disabledPlaceholders.forEach(span => {
      span.style.color = '#C0C4CC'
    })

    const disabledCarets = document.querySelectorAll('.el-select.is-disabled .el-select__caret')
    disabledCarets.forEach(caret => {
      caret.style.color = '#C0C4CC'
    })
  }, 100)
}

// 监听诊断变化，强制更新样式
watch(() => formData.diagnosisItemCode, () => {
  forceDisabledStyles()
}, { flush: 'post' })

// 组件挂载时加载数据
onMounted(async () => {
  // 先加载字典数据
  await loadDiagnosisOptions()

  // 再加载TNM分期记录数据
  if (props.patient?.visitSn) {
    await loadTnmStagingRecord()
  }

  // 强制设置禁用样式
  forceDisabledStyles()
})
</script>

<style scoped>
/* 整体容器 */
.tnm-staging-record-container {
  display: flex;
  width: 100%;
  height: calc(100vh - 180px); /* 使用视口高度减去导航栏高度，确保一屏显示 */
  gap: 20px;
}

/* 右侧记录内容区域 */
.record-content-area {
  flex: 1;
  height: 100%; /* 使用100%高度，与容器保持一致 */
  background: #FFFFFF;
  border-radius: 0 12px 12px 0;
  padding: 16px; /* 减少内边距以适应更多内容 */
  display: flex;
  flex-direction: column;
  overflow-y: auto; /* 添加垂直滚动，以防内容过多 */
}

/* 加载容器 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 400px;
}

/* 页面标题、时间信息和按钮行 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0; /* 防止压缩 */
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 40px;
}

.title-text {
  width: auto;
  height: 20px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 18px;
  color: #172B4D;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin: 0; /* 移除默认margin */
}

/* 时间信息头部 */
.time-info-section {
  display: flex;
  gap: 40px;
}

.time-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.time-label {
  width: 80px;
  height: 20px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #6B778C;
  line-height: 20px;
  text-align: left;
}

.time-value {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #172B4D;
  line-height: 20px;
}

/* 表单区域 */
.record-form {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center; /* 整个表单居中 */
  gap: 20px; /* 表单项之间的间距 */
  min-height: 0; /* 允许内容压缩 */
  max-width: 800px; /* 限制最大宽度 */
  margin: 0 auto; /* 水平居中 */
  padding: 20px;
}

/* 表单组布局 */
.form-group {
  display: flex;
  flex-direction: column; /* 垂直布局，标签和输入框分行 */
  gap: 8px; /* 标签和输入框之间的间距 */
  width: 100%; /* 占满容器宽度 */
  max-width: 700px; /* 限制最大宽度 */
}

/* 表单标签 */
.form-label {
  width: auto; /* 自动宽度 */
  height: 20px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #172B4D;
  line-height: 20px;
  text-align: left;
  flex-shrink: 0;
}

/* TNM输入框容器 */
.tnm-inputs {
  display: flex;
  gap: 21px;
  flex: 1;
}

/* 输入框样式 */
.form-input {
  width: 551px;
  flex-shrink: 0;
}

:deep(.form-input .el-input__wrapper) {
  width: 551px;

  border: 1px solid #EBECF0;
  border-radius: 0px;
  background-color: #FFFFFF;
  box-shadow: none;
  padding: 0 12px;
  transition: all 0.2s ease;
}

:deep(.form-input .el-input__wrapper:hover) {
  border-color: #1678FF;
}

:deep(.form-input .el-input__wrapper.is-focus) {
  border-color: #1678FF;

}

:deep(.form-input .el-input__inner) {
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  color: #172B4D;
  border: none;
  background: transparent;
  padding: 0;
}

:deep(.form-input .el-input__inner::placeholder) {
  color: #C1C7D0;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
}

/* TNM选择框样式 */
.form-select-tnm {
  width: 170px;
  flex-shrink: 0;
}

:deep(.form-select-tnm .el-select__wrapper) {
  width: 165px;
  border: 1px solid #EBECF0;
  border-radius: 0px;
  background-color: #FFFFFF;
  box-shadow: none;
  padding: 0 12px;
  transition: all 0.2s ease;
}

:deep(.form-select-tnm .el-select__wrapper:hover) {
  border-color: #1678FF;
}

:deep(.form-select-tnm .el-select__wrapper.is-focused) {
  border-color: #1678FF;
}

:deep(.form-select-tnm .el-select__input) {
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  color: #172B4D !important;
  border: none;
  background: transparent;
  padding: 0;
}

:deep(.form-select-tnm .el-select__selected-item) {
  color: #172B4D !important;
}

:deep(.form-select-tnm .el-select__placeholder) {
  color: #C1C7D0;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
}

:deep(.form-select-tnm .el-select__caret) {
  color: #6B778C;
  font-size: 14px;
}

:deep(.form-select-tnm .el-select__clear) {
  color: #6B778C;
  font-size: 14px;
}

:deep(.form-select-tnm .el-select__clear:hover) {
  color: #FF5630;
}

/* 日期选择器样式 */
.form-date-picker {
  width: 551px;
  flex-shrink: 0;
}

:deep(.form-date-picker .el-input__wrapper) {
  width: 551px;

  border: 1px solid #EBECF0;
  border-radius: 0px;
  background-color: #FFFFFF;
  box-shadow: none;
  padding: 0 12px;
  transition: all 0.2s ease;
}

:deep(.form-date-picker .el-input__wrapper:hover) {
  border-color: #1678FF;
}

:deep(.form-date-picker .el-input__wrapper.is-focus) {
  border-color: #1678FF;

}

:deep(.form-date-picker .el-input__inner) {
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  color: #172B4D;

  border: none;
  background: transparent;
  padding: 0;
}

:deep(.form-date-picker .el-input__inner::placeholder) {
  color: #C1C7D0;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
}



:deep(.form-date-picker .el-input__suffix-inner) {
  color: #6B778C;
}

/* 下拉选择器样式 */
.form-select {
  width: 551px;
  flex-shrink: 0;
}

:deep(.form-select .el-select__wrapper) {
  width: 551px;

  border: 1px solid #EBECF0;
  border-radius: 0px;
  background-color: #FFFFFF;
  box-shadow: none;
  padding: 0 12px;
  transition: all 0.2s ease;
}

:deep(.form-select .el-select__wrapper:hover) {
  border-color: #1678FF;
}

:deep(.form-select .el-select__wrapper.is-focused) {
  border-color: #1678FF;

}

:deep(.form-select .el-select__input) {
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  color: #172B4D !important;
  border: none;
  background: transparent;
  padding: 0;
}

/* 选中内容的样式 */
:deep(.form-select .el-select__selected-item) {
  color: #172B4D !important;
}

/* 选择器内部文本样式 */
:deep(.form-select .el-select__wrapper .el-select__selected-item) {
  color: #172B4D !important;
}

/* 确保选中的值显示为黑色 */
:deep(.form-select .el-select__wrapper .el-select__input-wrapper) {
  color: #172B4D !important;
}

:deep(.form-select .el-select__placeholder) {
  color: #C1C7D0;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
}

:deep(.form-select .el-select__caret) {
  color: #6B778C;
  font-size: 14px;
}

:deep(.form-select .el-select__clear) {
  color: #6B778C;
  font-size: 14px;
}

:deep(.form-select .el-select__clear:hover) {
  color: #FF5630;
}

/* 强制设置选中值的颜色 */
:deep(.form-select .el-select__wrapper .el-select__input-wrapper .el-select__input) {
  color: #172B4D !important;
}

/* 选择器显示文本的颜色 */
:deep(.form-select .el-select__wrapper .el-select__input-wrapper .el-select__input-inner) {
  color: #172B4D !important;
}

/* 选择器的值显示 */
:deep(.form-select .el-select__wrapper .el-select__input-wrapper .el-select__input .el-select__input-inner) {
  color: #172B4D !important;
}

/* 禁用状态样式 */
:deep(.el-input.is-disabled .el-input__wrapper) {
  background-color: #F5F5F5;
  border-color: #E4E7ED;
  color: #C0C4CC;
}

:deep(.el-input.is-disabled .el-input__inner) {
  color: #C0C4CC;
  cursor: default;
}

/* 选择框禁用状态样式 */
:deep(.el-select.is-disabled) {
  cursor: default !important;
  pointer-events: none !important;
}

:deep(.el-select.is-disabled .el-select__wrapper) {
  background-color: #F5F5F5 !important;
  border-color: #E4E7ED !important;
  cursor: default !important;
  pointer-events: none !important;
}

:deep(.el-select.is-disabled .el-select__wrapper:hover) {
  background-color: #F5F5F5 !important;
  border-color: #E4E7ED !important;
}

:deep(.el-select.is-disabled .el-select__input) {
  color: #C0C4CC !important;
  cursor: default !important;
}

:deep(.el-select.is-disabled .el-select__placeholder) {
  color: #C0C4CC !important;
}

:deep(.el-select.is-disabled .el-select__placeholder span) {
  color: #C0C4CC !important;
}

:deep(.el-select.is-disabled .el-select__caret) {
  color: #C0C4CC !important;
}

:deep(.el-select.is-disabled .el-select__suffix) {
  color: #C0C4CC !important;
}

:deep(.el-select.is-disabled .el-select__selected-item) {
  color: #C0C4CC !important;
}

/* 全局选择框禁用样式（不使用:deep） */
.el-select.is-disabled .el-select__wrapper {
  background-color: #F5F5F5 !important;
  border-color: #E4E7ED !important;
  cursor: default !important;
  pointer-events: none !important;
}

.el-select.is-disabled .el-select__wrapper:hover {
  background-color: #F5F5F5 !important;
  border-color: #E4E7ED !important;
}

.el-select.is-disabled .el-select__placeholder span {
  color: #C0C4CC !important;
}

/* 按钮区域 */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  flex-shrink: 0; /* 防止压缩 */
}

.delete-btn {
  width: 80px;
  height: 36px;
  background: #FFFFFF;
  border: 1px solid #FF5630;
  border-radius: 4px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #FF5630;
  line-height: 20px;
}

.delete-btn:hover {
  background: #FFF2F0;
  border-color: #FF5630;
  color: #FF5630;
}

.save-btn {
  width: 80px;
  height: 36px;
  background: #1678FF;
  border: 1px solid #1678FF;
  border-radius: 4px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  line-height: 20px;
}

.save-btn:hover {
  background: #0052CC;
  border-color: #0052CC;
}

/* 按钮加载状态 */
:deep(.delete-btn.is-loading),
:deep(.save-btn.is-loading) {
  pointer-events: none;
}

/* 响应式适配 */
@media (max-width: 1366px) {
  .form-label {
    width: 120px;
    min-width: 120px;
    font-size: 14px;
  }

  .form-item,
  .form-item-half,
  .form-item-third {
    min-height: 36px;
  }

  :deep(.form-input .el-input__wrapper),
  :deep(.form-date-picker .el-input__wrapper) {
    height: 28px;
  }

  :deep(.form-input .el-input__inner),
  :deep(.form-date-picker .el-input__inner) {
    height: 26px;
    line-height: 26px;
    font-size: 13px;
  }
}
</style>

<style>
/* 全局选择框禁用样式 - 不使用scoped确保能覆盖Element Plus默认样式 */
.el-select.is-disabled .el-select__wrapper {
  background-color: #F5F5F5 !important;
  border-color: #E4E7ED !important;
  cursor: default !important;
  pointer-events: none !important;
}

.el-select.is-disabled .el-select__wrapper:hover {
  background-color: #F5F5F5 !important;
  border-color: #E4E7ED !important;
}

.el-select.is-disabled .el-select__placeholder span {
  color: #C0C4CC !important;
}

.el-select.is-disabled .el-select__input {
  color: #C0C4CC !important;
}

.el-select.is-disabled .el-select__caret {
  color: #C0C4CC !important;
}

.el-select.is-disabled {
  cursor: default !important;
  pointer-events: none !important;
}

/* 更强的选择器 - 针对具体的类名 */
.form-select-tnm.el-select.is-disabled .el-select__wrapper {
  background-color: #F5F5F5 !important;
  border-color: #E4E7ED !important;
  cursor: default !important;
  pointer-events: none !important;
}

.form-select.el-select.is-disabled .el-select__wrapper {
  background-color: #F5F5F5 !important;
  border-color: #E4E7ED !important;
  cursor: default !important;
  pointer-events: none !important;
}

/* 使用属性选择器 */
.el-select[disabled] .el-select__wrapper,
.el-select.is-disabled .el-select__wrapper {
  background-color: #F5F5F5 !important;
  border-color: #E4E7ED !important;
  cursor: default !important;
  pointer-events: none !important;
}

/* 最高优先级 */
div.el-select.is-disabled div.el-select__wrapper {
  background-color: #F5F5F5 !important;
  border-color: #E4E7ED !important;
  cursor: default !important;
  pointer-events: none !important;
}

/* 自定义禁用类 - 最强选择器 */
.el-select.custom-disabled .el-select__wrapper,
.el-select.custom-disabled.is-disabled .el-select__wrapper {
  background-color: #F5F5F5 !important;
  border-color: #E4E7ED !important;
  cursor: default !important;
  pointer-events: none !important;
}

.el-select.custom-disabled .el-select__wrapper:hover,
.el-select.custom-disabled.is-disabled .el-select__wrapper:hover {
  background-color: #F5F5F5 !important;
  border-color: #E4E7ED !important;
}

.el-select.custom-disabled .el-select__placeholder span,
.el-select.custom-disabled.is-disabled .el-select__placeholder span {
  color: #C0C4CC !important;
}

.el-select.custom-disabled .el-select__caret,
.el-select.custom-disabled.is-disabled .el-select__caret {
  color: #C0C4CC !important;
}

.el-select.custom-disabled,
.el-select.custom-disabled.is-disabled {
  cursor: default !important;
  pointer-events: none !important;
}

/* 使用内联样式的方式 */
.custom-disabled[style*="background"] {
  background-color: #F5F5F5 !important;
}
</style>
